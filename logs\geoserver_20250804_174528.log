2025-08-04 17:45:28,596 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250804_174528.log
2025-08-04 17:45:28,610 - geo_publisher - INFO - 加载了 39 个任务状态
2025-08-04 17:45:28,780 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 17:45:28,781 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 17:45:28,794 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 17:45:28,802 - root - INFO - === GeoServer REST API服务 ===
2025-08-04 17:45:28,802 - root - INFO - 主机: 0.0.0.0
2025-08-04 17:45:28,802 - root - INFO - 端口: 5083
2025-08-04 17:45:28,803 - root - INFO - 调试模式: 禁用
2025-08-04 17:45:28,804 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-08-04 17:45:28,829 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-08-04 17:45:28,830 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-04 17:45:30,986 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 17:45:30] "[33mGET /api/layers/?workspace=test HTTP/1.1[0m" 404 -
2025-08-04 17:46:11,623 - werkzeug - INFO - 127.0.0.1 - - [04/Aug/2025 17:46:11] "[33mGET /api/layers/?workspace=test HTTP/1.1[0m" 404 -
2025-08-04 17:46:19,174 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 17:46:19,178 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 17:46:19,196 - map_api - INFO - 找到 4 个目录
2025-08-04 17:46:19,198 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 17:46:19,209 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 17:46:19,210 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 17:46:19,217 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 17:46:19,219 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 17:46:19,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 17:46:19,233 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 17:46:19,260 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 17:46:19,265 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 17:46:19,268 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 17:46:19,271 - werkzeug - INFO - 192.168.43.148 - - [04/Aug/2025 17:46:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-04 17:47:19,222 - map_api - INFO - 开始获取ODM任务列表
2025-08-04 17:47:19,232 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-04 17:47:19,248 - map_api - INFO - 找到 4 个目录
2025-08-04 17:47:19,250 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-04 17:47:19,262 - map_api - INFO - 成功获取文件 ODM/Input/20250705171599/TaskInfo.json, 内容大小: 3064 字节
2025-08-04 17:47:19,264 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-04 17:47:19,271 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-04 17:47:19,277 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-04 17:47:19,285 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-04 17:47:19,289 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-04 17:47:19,312 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-04 17:47:19,315 - map_api - INFO - 获取到 4 个任务信息
2025-08-04 17:47:19,322 - map_api - INFO - 任务排序结果: 运行中(1) > 未开始(0) > 已完成(3)
2025-08-04 17:47:19,325 - werkzeug - INFO - 192.168.43.148 - - [04/Aug/2025 17:47:19] "GET /api/map/odm/tasks HTTP/1.1" 200 -
