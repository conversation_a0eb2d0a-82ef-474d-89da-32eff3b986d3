2025-07-31 16:54:41,216 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250731_165441.log
2025-07-31 16:54:41,232 - geo_publisher - INFO - 加载了 32 个任务状态
2025-07-31 16:54:41,342 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-07-31 16:54:41,343 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-07-31 16:54:41,356 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-07-31 16:54:41,366 - root - INFO - === GeoServer REST API服务 ===
2025-07-31 16:54:41,368 - root - INFO - 主机: 0.0.0.0
2025-07-31 16:54:41,369 - root - INFO - 端口: 5083
2025-07-31 16:54:41,370 - root - INFO - 调试模式: 禁用
2025-07-31 16:54:41,371 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-07-31 16:54:41,392 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-07-31 16:54:41,393 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-31 16:54:44,022 - map_api - INFO - 开始获取ODM任务列表
2025-07-31 16:54:44,029 - map_api - INFO - 请求目录列表: ODM/Input
2025-07-31 16:54:44,043 - map_api - INFO - 找到 3 个目录
2025-07-31 16:54:44,047 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-07-31 16:54:44,054 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-07-31 16:54:44,059 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-07-31 16:54:44,088 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-07-31 16:54:44,093 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-07-31 16:54:44,100 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-07-31 16:54:44,103 - map_api - INFO - 获取到 3 个任务信息
2025-07-31 16:54:44,107 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(0) > 已完成(3)
2025-07-31 16:54:44,111 - werkzeug - INFO - 192.168.43.148 - - [31/Jul/2025 16:54:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
