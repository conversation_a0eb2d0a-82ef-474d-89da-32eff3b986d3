2025-08-04 16:14:22,850 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250804_161422.log
2025-08-04 16:14:22,864 - geo_publisher - INFO - 加载了 39 个任务状态
2025-08-04 16:14:22,987 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-04 16:14:22,987 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-04 16:14:23,001 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-04 16:14:23,006 - root - INFO - === GeoServer REST API服务 ===
2025-08-04 16:14:23,007 - root - INFO - 主机: 0.0.0.0
2025-08-04 16:14:23,007 - root - INFO - 端口: 5083
2025-08-04 16:14:23,009 - root - INFO - 调试模式: 禁用
2025-08-04 16:14:23,010 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-08-04 16:14:23,025 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-08-04 16:14:23,026 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
