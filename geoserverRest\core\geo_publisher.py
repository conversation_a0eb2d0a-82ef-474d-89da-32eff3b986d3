#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer Publisher模块
用于异步发布GeoServer图层
"""

import os
import sys
import json
import uuid
import logging
import datetime
import threading
import traceback

# 导入GeoServer管理模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from geoserver_manager import GeoServerManager, logger as geoserver_logger

# 定义日志目录
GEOLOG_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'geolog')
os.makedirs(GEOLOG_DIR, exist_ok=True)

# 配置日志
geo_logger = logging.getLogger('geo_publisher')
geo_logger.setLevel(logging.INFO)

# 添加控制台处理器
console_handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
geo_logger.addHandler(console_handler)

# 添加文件处理器
process_log_handler = logging.FileHandler(
    os.path.join(GEOLOG_DIR, 'geo_process.log'),
    encoding='utf-8'
)
process_log_handler.setFormatter(formatter)
geo_logger.addHandler(process_log_handler)

class GeoStatus:
    """GeoServer发布任务状态常量"""
    RUNNING = "正在运行"
    SUCCESS = "发布成功"
    FAILED = "发布失败"

class GeoServerExecutor:
    """GeoServer图层发布执行器"""
    
    def __init__(self):
        """初始化GeoServer发布执行器"""
        self.geo_tasks = {}  # 存储任务ID和状态
        self.tasks_lock = threading.Lock()  # 用于线程安全的操作
        self.status_file = os.path.join(GEOLOG_DIR, 'geo_status.json')
        self._load_tasks()
    
    def _load_tasks(self):
        """从文件加载任务状态"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    self.geo_tasks = json.load(f)
                    geo_logger.info(f"加载了 {len(self.geo_tasks)} 个任务状态")
        except Exception as e:
            geo_logger.error(f"加载任务状态失败: {str(e)}")
    
    def _save_tasks(self):
        """保存任务状态到文件"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.geo_tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            geo_logger.error(f"保存任务状态失败: {str(e)}")
    
    def execute_publish_shapefile(self, shapefile_path, workspace, store_name=None, layer_name=None, charset="UTF-8"):
        """
        异步执行shapefile发布任务
        
        参数:
            shapefile_path: shapefile文件路径
            workspace: 工作区名称
            store_name: 存储名称（可选）
            layer_name: 图层名称（可选）
            charset: 字符集，默认UTF-8
            
        返回:
            task_id: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 必需参数检查
        if not shapefile_path or not workspace:
            geo_logger.error("缺少必要参数shapefile_path或workspace")
            return None
        
        # 创建任务日志文件
        log_file_path = os.path.join(GEOLOG_DIR, f"{task_id}.log")
        
        # 记录任务信息
        task_info = {
            'task_id': task_id,
            'type': 'publish_shapefile',
            'parameters': {
                'shapefile_path': shapefile_path,
                'workspace': workspace,
                'store_name': store_name,
                'layer_name': layer_name,
                'charset': charset
            },
            'status': GeoStatus.RUNNING,
            'start_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': None,
            'log_file': log_file_path
        }
        
        with self.tasks_lock:
            self.geo_tasks[task_id] = task_info
            self._save_tasks()
        
        # 启动执行线程
        thread = threading.Thread(
            target=self._run_publish_shapefile,
            args=(task_id, shapefile_path, workspace, store_name, layer_name, charset, log_file_path),
            daemon=True
        )
        thread.start()
        
        geo_logger.info(f"启动Shapefile发布任务 {task_id}: {shapefile_path}, workspace={workspace}")
        return task_id
    
    def execute_publish_geotiff(self, geotiff_path, workspace, store_name=None, layer_name=None):
        """
        异步执行GeoTIFF发布任务
        
        参数:
            geotiff_path: GeoTIFF文件路径
            workspace: 工作区名称
            store_name: 存储名称（可选）
            layer_name: 图层名称（可选）
            
        返回:
            task_id: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 必需参数检查
        if not geotiff_path or not workspace:
            geo_logger.error("缺少必要参数geotiff_path或workspace")
            return None
        
        # 创建任务日志文件
        log_file_path = os.path.join(GEOLOG_DIR, f"{task_id}.log")
        
        # 记录任务信息
        task_info = {
            'task_id': task_id,
            'type': 'publish_geotiff',
            'parameters': {
                'geotiff_path': geotiff_path,
                'workspace': workspace,
                'store_name': store_name,
                'layer_name': layer_name
            },
            'status': GeoStatus.RUNNING,
            'start_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': None,
            'log_file': log_file_path
        }
        
        with self.tasks_lock:
            self.geo_tasks[task_id] = task_info
            self._save_tasks()
        
        # 启动执行线程
        thread = threading.Thread(
            target=self._run_publish_geotiff,
            args=(task_id, geotiff_path, workspace, store_name, layer_name, log_file_path),
            daemon=True
        )
        thread.start()
        
        geo_logger.info(f"启动GeoTIFF发布任务 {task_id}: {geotiff_path}, workspace={workspace}")
        return task_id
    
    def execute_publish_shapefile_directory(self, directory_path, workspace, store_name=None, charset="UTF-8"):
        """
        异步执行shapefile目录发布任务
        
        参数:
            directory_path: shapefile目录路径
            workspace: 工作区名称
            store_name: 存储名称（可选）
            charset: 字符集，默认UTF-8
            
        返回:
            task_id: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 必需参数检查
        if not directory_path or not workspace:
            geo_logger.error("缺少必要参数directory_path或workspace")
            return None
        
        # 创建任务日志文件
        log_file_path = os.path.join(GEOLOG_DIR, f"{task_id}.log")
        
        # 记录任务信息
        task_info = {
            'task_id': task_id,
            'type': 'publish_shapefile_directory',
            'parameters': {
                'directory_path': directory_path,
                'workspace': workspace,
                'store_name': store_name,
                'charset': charset
            },
            'status': GeoStatus.RUNNING,
            'start_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': None,
            'log_file': log_file_path
        }
        
        with self.tasks_lock:
            self.geo_tasks[task_id] = task_info
            self._save_tasks()
        
        # 启动执行线程
        thread = threading.Thread(
            target=self._run_publish_shapefile_directory,
            args=(task_id, directory_path, workspace, store_name, charset, log_file_path),
            daemon=True
        )
        thread.start()
        
        geo_logger.info(f"启动Shapefile目录发布任务 {task_id}: {directory_path}, workspace={workspace}")
        return task_id
    
    def execute_publish_geotiff_directory(self, directory_path, workspace, store_name=None):
        """
        异步执行GeoTIFF目录发布任务
        
        参数:
            directory_path: GeoTIFF目录路径
            workspace: 工作区名称
            store_name: 存储名称（可选）
            
        返回:
            task_id: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 必需参数检查
        if not directory_path or not workspace:
            geo_logger.error("缺少必要参数directory_path或workspace")
            return None
        
        # 创建任务日志文件
        log_file_path = os.path.join(GEOLOG_DIR, f"{task_id}.log")
        
        # 记录任务信息
        task_info = {
            'task_id': task_id,
            'type': 'publish_geotiff_directory',
            'parameters': {
                'directory_path': directory_path,
                'workspace': workspace,
                'store_name': store_name
            },
            'status': GeoStatus.RUNNING,
            'start_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': None,
            'log_file': log_file_path
        }
        
        with self.tasks_lock:
            self.geo_tasks[task_id] = task_info
            self._save_tasks()
        
        # 启动执行线程
        thread = threading.Thread(
            target=self._run_publish_geotiff_directory,
            args=(task_id, directory_path, workspace, store_name, log_file_path),
            daemon=True
        )
        thread.start()
        
        geo_logger.info(f"启动GeoTIFF目录发布任务 {task_id}: {directory_path}, workspace={workspace}")
        return task_id
    
    def _run_with_logging(self, task_id, log_file_path, func, *args, **kwargs):
        """
        使用日志记录运行函数
        
        参数:
            task_id: 任务ID
            log_file_path: 日志文件路径
            func: 要执行的函数
            *args, **kwargs: 函数参数
        """
        try:
            # 配置日志记录到文件
            task_logger = logging.getLogger(f'geo_task_{task_id}')
            task_logger.setLevel(logging.INFO)
            
            # 清除任何现有处理器
            if task_logger.handlers:
                for handler in task_logger.handlers:
                    task_logger.removeHandler(handler)
                    
            # 添加文件处理器
            file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
            file_handler.setFormatter(formatter)
            task_logger.addHandler(file_handler)
            
            # 将输出重定向到日志
            original_stdout = sys.stdout
            original_stderr = sys.stderr
            
            class LoggerWriter:
                def __init__(self, logger, level):
                    self.logger = logger
                    self.level = level
                    self.buf = ""
                
                def write(self, message):
                    if message and not message.isspace():
                        self.buf += message
                        if self.buf.endswith('\n'):
                            self.logger.log(self.level, self.buf.rstrip())
                            self.buf = ""
                
                def flush(self):
                    if self.buf:
                        self.logger.log(self.level, self.buf)
                        self.buf = ""
            
            sys.stdout = LoggerWriter(task_logger, logging.INFO)
            sys.stderr = LoggerWriter(task_logger, logging.ERROR)
            
            # 记录启动信息
            task_logger.info(f"GeoServer发布任务 {task_id} 开始执行")
            task_logger.info(f"开始时间: {self.geo_tasks[task_id]['start_time']}")
            task_logger.info(f"参数: {json.dumps(self.geo_tasks[task_id]['parameters'], ensure_ascii=False, indent=2)}")
            
            # 创建GeoServer管理器
            manager = GeoServerManager()
            
            # 执行发布操作，将task_logger作为参数传递给内部函数
            def wrapper_func(*args, **kwargs):
                # 将task_logger设为全局变量，以便内部函数使用
                global task_logger
                return func(*args, **kwargs)
                
            result = wrapper_func(manager, *args, **kwargs)
            
            # 更新状态
            with self.tasks_lock:
                self.geo_tasks[task_id]['end_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.geo_tasks[task_id]['status'] = GeoStatus.SUCCESS if result else GeoStatus.FAILED
                self.geo_tasks[task_id]['result'] = result
                self._save_tasks()
            
            # 记录完成信息
            status_str = "成功" if result else "失败"
            task_logger.info(f"GeoServer发布任务 {task_id} 执行{status_str}")
            task_logger.info(f"完成时间: {self.geo_tasks[task_id]['end_time']}")
            task_logger.info(f"状态: {self.geo_tasks[task_id]['status']}")
            
            # 恢复原始输出
            sys.stdout = original_stdout
            sys.stderr = original_stderr
            
            return result
            
        except Exception as e:
            geo_logger.error(f"执行任务 {task_id} 时出错: {str(e)}")
            geo_logger.error(traceback.format_exc())
            
            # 更新状态为失败
            with self.tasks_lock:
                self.geo_tasks[task_id]['end_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.geo_tasks[task_id]['status'] = GeoStatus.FAILED
                self.geo_tasks[task_id]['error'] = str(e)
                self._save_tasks()
            
            # 记录错误到日志文件
            try:
                # 确保任务日志记录器存在
                if 'task_logger' not in locals() or not task_logger.handlers:
                    task_logger = logging.getLogger(f'geo_task_{task_id}')
                    task_logger.setLevel(logging.INFO)
                    file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
                    file_handler.setFormatter(formatter)
                    task_logger.addHandler(file_handler)
                
                task_logger.error(f"执行出错: {str(e)}")
                task_logger.error(traceback.format_exc())
                task_logger.error(f"完成时间: {self.geo_tasks[task_id]['end_time']}")
                task_logger.error(f"状态: {GeoStatus.FAILED}")
                
                # 恢复原始输出
                if 'original_stdout' in locals():
                    sys.stdout = original_stdout
                    sys.stderr = original_stderr
            except Exception as log_error:
                geo_logger.error(f"记录错误信息时发生异常: {str(log_error)}")
            
            return False
    
    def _run_publish_shapefile(self, task_id, shapefile_path, workspace, store_name, layer_name, charset, log_file_path):
        """
        执行shapefile发布任务
        """
        def publish_func(manager, path, ws, store, layer, cs):
            # 检查图层是否已存在，如果存在则先删除
            if store and layer:
                if manager.verify_layer(ws, store, layer):
                    task_logger.info(f"图层 '{ws}:{layer}' 已存在，正在删除...")
                    manager.delete_layer(layer, ws)
                    task_logger.info(f"已删除已存在的图层 '{ws}:{layer}'")
                    
            return manager.publish_shapefile(path, ws, store, layer, cs)
        
        return self._run_with_logging(
            task_id, log_file_path, publish_func,
            shapefile_path, workspace, store_name, layer_name, charset
        )
    
    def _run_publish_geotiff(self, task_id, geotiff_path, workspace, store_name, layer_name, log_file_path):
        """
        执行GeoTIFF发布任务
        """
        def publish_func(manager, path, ws, store, layer):
            # 检查图层是否已存在，如果存在则先删除
            if store and layer:
                if manager.verify_layer(ws, store, layer):
                    task_logger.info(f"图层 '{ws}:{layer}' 已存在，正在删除...")
                    manager.delete_layer(layer, ws)
                    task_logger.info(f"已删除已存在的图层 '{ws}:{layer}'")
            # 如果图层名称与存储名称相同，也需要检查
            elif store and store == layer:
                if manager.verify_layer(ws, store, store):
                    task_logger.info(f"图层 '{ws}:{store}' 已存在，正在删除...")
                    manager.delete_layer(store, ws)
                    task_logger.info(f"已删除已存在的图层 '{ws}:{store}'")
                    
            return manager.publish_geotiff(path, ws, store, layer)
        
        return self._run_with_logging(
            task_id, log_file_path, publish_func,
            geotiff_path, workspace, store_name, layer_name
        )
    
    def _run_publish_shapefile_directory(self, task_id, directory_path, workspace, store_name, charset, log_file_path):
        """
        执行shapefile目录发布任务
        """
        def publish_func(manager, path, ws, store, cs):
            # 如果使用了共同的store，先获取目录中的所有shp文件
            if store:
                import glob
                import os
                shapefile_paths = glob.glob(os.path.join(path, "*.shp"))
                
                for shapefile_path in shapefile_paths:
                    base_name = os.path.splitext(os.path.basename(shapefile_path))[0]
                    # 检查图层是否已存在，如果存在则先删除
                    if manager.verify_layer(ws, store, base_name):
                        task_logger.info(f"图层 '{ws}:{base_name}' 已存在，正在删除...")
                        manager.delete_layer(base_name, ws)
                        task_logger.info(f"已删除已存在的图层 '{ws}:{base_name}'")
                
            return manager.publish_shapefile_directory(path, ws, store, cs)
        
        return self._run_with_logging(
            task_id, log_file_path, publish_func,
            directory_path, workspace, store_name, charset
        )
    
    def _run_publish_geotiff_directory(self, task_id, directory_path, workspace, store_name, log_file_path):
        """
        执行GeoTIFF目录发布任务
        """
        def publish_func(manager, path, ws, store):
            # 获取目录中的所有GeoTIFF文件
            import glob
            import os
            geotiff_paths = []
            for ext in ['.tif', '.tiff']:
                geotiff_paths.extend(glob.glob(os.path.join(path, f"*{ext}")))
                
            # 对每个文件检查图层是否存在
            for geotiff_path in geotiff_paths:
                base_name = os.path.splitext(os.path.basename(geotiff_path))[0]
                # 对于目录发布，通常会使用store_prefix_basename作为存储名
                current_store_name = f"{store}_{base_name}" if store else base_name
                
                # 检查图层是否已存在，如果存在则先删除
                if manager.verify_layer(ws, current_store_name, base_name):
                    task_logger.info(f"图层 '{ws}:{base_name}' 已存在，正在删除...")
                    manager.delete_layer(base_name, ws)
                    task_logger.info(f"已删除已存在的图层 '{ws}:{base_name}'")
                
            return manager.publish_geotiff_directory(path, ws, store)
        
        return self._run_with_logging(
            task_id, log_file_path, publish_func,
            directory_path, workspace, store_name
        )
    
    def get_task_status(self, task_id):
        """
        获取任务状态
        
        参数:
            task_id: 任务ID
            
        返回:
            任务状态信息，如果未找到则返回None
        """
        with self.tasks_lock:
            return self.geo_tasks.get(task_id)
    
    def get_all_tasks(self):
        """
        获取所有任务
        
        返回:
            所有任务状态信息
        """
        with self.tasks_lock:
            return self.geo_tasks
    
    def clear_completed_tasks(self, hours=24):
        """
        清除已完成的旧任务
        
        参数:
            hours: 保留最近多少小时内的任务，默认24小时
        """
        with self.tasks_lock:
            now = datetime.datetime.now()
            to_remove = []
            
            for task_id, task_info in self.geo_tasks.items():
                if task_info['status'] in [GeoStatus.SUCCESS, GeoStatus.FAILED]:
                    end_time = datetime.datetime.strptime(task_info['end_time'], '%Y-%m-%d %H:%M:%S')
                    if (now - end_time).total_seconds() > hours * 3600:
                        to_remove.append(task_id)
            
            for task_id in to_remove:
                del self.geo_tasks[task_id]
                
            if to_remove:
                self._save_tasks()
                geo_logger.info(f"清除了 {len(to_remove)} 个已完成的旧任务")

# 创建全局执行器实例
geo_executor = GeoServerExecutor() 