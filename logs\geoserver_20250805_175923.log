2025-08-05 17:59:23,335 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250805_175923.log
2025-08-05 17:59:23,358 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-05 17:59:23,529 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-05 17:59:23,530 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-05 17:59:23,543 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-05 17:59:23,551 - root - INFO - === GeoServer REST API服务 ===
2025-08-05 17:59:23,551 - root - INFO - 主机: 0.0.0.0
2025-08-05 17:59:23,552 - root - INFO - 端口: 5083
2025-08-05 17:59:23,554 - root - INFO - 调试模式: 禁用
2025-08-05 17:59:23,555 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-08-05 17:59:23,583 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-08-05 17:59:23,584 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-05 17:59:30,544 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:59:30,558 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:59:30,658 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:59:30,665 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:59:30] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:59:30,660 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:59:30,673 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:59:30,681 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:59:30] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:59:30,686 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:59:30,714 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:59:30] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:59:30,816 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:59:30,922 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:59:30,969 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:59:30,971 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:59:31,044 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:59:31] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:59:31,047 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:59:31,137 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:59:31] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:59:31,154 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:59:31,164 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:59:31] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:59:32,414 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:59:32] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:59:32,603 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:59:32,658 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:59:32,681 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:59:32] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 18:04:55,992 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 18:04:56,013 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 18:04:56,070 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 18:04:56,169 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 18:04:56] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 18:04:56,088 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 18:04:56,173 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 18:04:56,288 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 18:04:56] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 18:04:56,333 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 18:04:56,339 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 18:04:56,340 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 18:04:56,351 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 18:04:56,362 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 18:04:56] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 18:04:56,380 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 18:04:56,383 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 18:04:56,397 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 18:04:56] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 18:04:56,384 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 18:04:56,404 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 18:04:56] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 18:04:56,419 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 18:04:56] "GET /api/map/base-map2 HTTP/1.1" 200 -
