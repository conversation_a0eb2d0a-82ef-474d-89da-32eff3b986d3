2025-08-05 10:58:27,209 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverapi\logs\geoserver_20250805_105827.log
2025-08-05 10:58:27,233 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-05 10:58:27,378 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-05 10:58:27,378 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-05 10:58:27,390 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-05 10:58:27,395 - root - INFO - === GeoServer REST API服务 ===
2025-08-05 10:58:27,396 - root - INFO - 主机: 0.0.0.0
2025-08-05 10:58:27,396 - root - INFO - 端口: 5083
2025-08-05 10:58:27,397 - root - INFO - 调试模式: 禁用
2025-08-05 10:58:27,398 - root - INFO - 启动GeoServer REST API服务，监听 0.0.0.0:5083
2025-08-05 10:58:27,420 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5083
 * Running on http://**********:5083
2025-08-05 10:58:27,421 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-05 10:59:20,045 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 10:59:20,051 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 10:59:20,071 - map_api - INFO - 找到 4 个目录
2025-08-05 10:59:20,073 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 10:59:20,080 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 10:59:20,083 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 10:59:20,089 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 10:59:20,090 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 10:59:20,143 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 10:59:20,145 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 10:59:20,153 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 10:59:20,154 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 10:59:20,155 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 10:59:20,159 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 10:59:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:00:20,047 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:00:20,056 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:00:20,063 - map_api - INFO - 找到 4 个目录
2025-08-05 11:00:20,065 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:00:20,070 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:00:20,071 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:00:20,080 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:00:20,082 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:00:20,087 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:00:20,092 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:00:20,100 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:00:20,101 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:00:20,102 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:00:20,108 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:00:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:00:29,625 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:00:29,686 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:00:29,820 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:00:29,846 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:00:29] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:00:29,828 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 11:00:29,877 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:00:29,885 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:00:29] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:00:29,928 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:00:29,935 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 11:00:29,940 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:00:29] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 11:00:29,954 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:00:29,969 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:00:29,984 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:00:29] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:00:29,989 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:00:29,992 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:00:29] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:00:30,012 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:00:30,041 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:00:30,187 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:00:30] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:00:31,743 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:00:31] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:00:31,909 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 11:00:31,971 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 11:00:32,074 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:00:32] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:01:20,054 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:01:20,058 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:01:20,066 - map_api - INFO - 找到 4 个目录
2025-08-05 11:01:20,068 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:01:20,074 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:01:20,075 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:01:20,094 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:01:20,098 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:01:20,125 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:01:20,129 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:01:20,156 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:01:20,159 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:01:20,160 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:01:20,162 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:01:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:02:20,079 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:02:20,096 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:02:20,110 - map_api - INFO - 找到 4 个目录
2025-08-05 11:02:20,115 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:02:20,122 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:02:20,124 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:02:20,131 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:02:20,140 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:02:20,146 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:02:20,148 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:02:20,157 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:02:20,159 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:02:20,161 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:02:20,174 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:02:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:03:20,046 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:03:20,049 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:03:20,059 - map_api - INFO - 找到 4 个目录
2025-08-05 11:03:20,061 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:03:20,089 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:03:20,093 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:03:20,119 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:03:20,124 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:03:20,129 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:03:20,131 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:03:20,137 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:03:20,163 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:03:20,187 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:03:20,205 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:03:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:04:20,149 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:04:20,155 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:04:20,168 - map_api - INFO - 找到 4 个目录
2025-08-05 11:04:20,172 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:04:20,177 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:04:20,179 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:04:20,196 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:04:20,201 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:04:20,208 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:04:20,210 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:04:20,218 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:04:20,225 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:04:20,232 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:04:20,247 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:04:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:04:42,859 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:04:42,924 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:04:43,056 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:04:43,282 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:04:43] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:04:43,283 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 11:04:43,316 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:04:43,328 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:04:43] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:04:43,317 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 11:04:43,336 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:04:43] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 11:04:43,432 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:04:43,456 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:04:43,467 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:04:43,471 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:04:43,474 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:04:43] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:04:43,476 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:04:43] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:04:43,503 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:04:43,525 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:04:43,561 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:04:43] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:04:45,352 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:04:45] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:04:45,381 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 11:04:45,437 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 11:04:45,449 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:04:45] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:05:20,108 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:05:20,111 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:05:20,123 - map_api - INFO - 找到 4 个目录
2025-08-05 11:05:20,130 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:05:20,137 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:05:20,138 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:05:20,145 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:05:20,149 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:05:20,158 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:05:20,161 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:05:20,168 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:05:20,171 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:05:20,177 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:05:20,179 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:05:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:05:37,902 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:05:37,923 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:05:37,931 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:05:37] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:05:38,955 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:05:38,962 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:05:38,971 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:05:38,973 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:05:38,976 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:05:38] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:05:38,978 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:05:38] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:06:20,109 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:06:20,117 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:06:20,131 - map_api - INFO - 找到 4 个目录
2025-08-05 11:06:20,134 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:06:20,145 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:06:20,147 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:06:20,153 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:06:20,157 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:06:20,174 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:06:20,176 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:06:20,202 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:06:20,209 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:06:20,211 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:06:20,214 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:06:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:07:20,076 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:07:20,081 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:07:20,091 - map_api - INFO - 找到 4 个目录
2025-08-05 11:07:20,093 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:07:20,099 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:07:20,102 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:07:20,120 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:07:20,123 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:07:20,137 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:07:20,138 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:07:20,152 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:07:20,154 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:07:20,154 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:07:20,156 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:07:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:08:20,068 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:08:20,074 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:08:20,085 - map_api - INFO - 找到 4 个目录
2025-08-05 11:08:20,088 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:08:20,107 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:08:20,116 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:08:20,189 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:08:20,196 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:08:20,217 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:08:20,227 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:08:20,243 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:08:20,246 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:08:20,255 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:08:20,259 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:08:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:09:20,068 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:09:20,073 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:09:20,086 - map_api - INFO - 找到 4 个目录
2025-08-05 11:09:20,089 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:09:20,101 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:09:20,103 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:09:20,109 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:09:20,113 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:09:20,122 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:09:20,126 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:09:20,136 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:09:20,141 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:09:20,144 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:09:20,147 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:09:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:10:26,262 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:10:26,605 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:10:27,350 - map_api - INFO - 找到 4 个目录
2025-08-05 11:10:27,515 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:10:27,525 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:10:27,529 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:10:27,538 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:10:27,543 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:10:27,551 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:10:27,557 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:10:27,567 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:10:27,572 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:10:27,574 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:10:27,576 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:10:27] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:10:39,063 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:10:39,170 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 11:10:39,226 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:10:39,498 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:10:39,609 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 11:10:39,940 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:10:39] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:10:40,107 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:10:40] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 11:10:39,797 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:10:40,511 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:10:40] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:10:40,705 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:10:41,089 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:10:41,105 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:10:41,378 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:10:41,892 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:10:41] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:10:41,726 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:10:41,981 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:10:42,063 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:10:42] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:10:42,064 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:10:42] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:10:42,484 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:10:42] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:10:42,921 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 11:10:43,014 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 11:10:43,067 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:10:43] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:11:20,054 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:11:20,059 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:11:20,070 - map_api - INFO - 找到 4 个目录
2025-08-05 11:11:20,072 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:11:20,078 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:11:20,090 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:11:20,111 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:11:20,114 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:11:20,128 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:11:20,130 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:11:20,143 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:11:20,145 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:11:20,146 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:11:20,149 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:11:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:12:20,107 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:12:20,137 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:12:20,152 - map_api - INFO - 找到 4 个目录
2025-08-05 11:12:20,154 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:12:20,162 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:12:20,169 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:12:20,184 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:12:20,187 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:12:20,202 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:12:20,209 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:12:20,220 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:12:20,229 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:12:20,231 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:12:20,235 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:12:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:13:20,081 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:13:20,087 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:13:20,098 - map_api - INFO - 找到 4 个目录
2025-08-05 11:13:20,100 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:13:20,109 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:13:20,113 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:13:20,119 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:13:20,121 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:13:20,135 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:13:20,138 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:13:20,147 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:13:20,149 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:13:20,151 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:13:20,160 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:13:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:14:20,072 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:14:20,079 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:14:20,092 - map_api - INFO - 找到 4 个目录
2025-08-05 11:14:20,094 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:14:20,102 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:14:20,108 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:14:20,116 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:14:20,119 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:14:20,128 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:14:20,133 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:14:20,141 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:14:20,149 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:14:20,153 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:14:20,156 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:14:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:15:20,048 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:15:20,053 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:15:20,082 - map_api - INFO - 找到 4 个目录
2025-08-05 11:15:20,085 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:15:20,109 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:15:20,114 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:15:20,121 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:15:20,123 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:15:20,141 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:15:20,142 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:15:20,172 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:15:20,179 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:15:20,184 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:15:20,188 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:15:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:16:20,055 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:16:20,059 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:16:20,070 - map_api - INFO - 找到 4 个目录
2025-08-05 11:16:20,072 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:16:20,080 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:16:20,083 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:16:20,109 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:16:20,120 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:16:20,128 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:16:20,132 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:16:20,138 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:16:20,142 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:16:20,146 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:16:20,151 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:16:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:17:20,067 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:17:20,074 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:17:20,085 - map_api - INFO - 找到 4 个目录
2025-08-05 11:17:20,087 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:17:20,092 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:17:20,094 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:17:20,125 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:17:20,128 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:17:20,135 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:17:20,137 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:17:20,144 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:17:20,147 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:17:20,151 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:17:20,155 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:17:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:18:20,097 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:18:20,103 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:18:20,117 - map_api - INFO - 找到 4 个目录
2025-08-05 11:18:20,119 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:18:20,127 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:18:20,136 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:18:20,142 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:18:20,144 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:18:20,157 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:18:20,158 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:18:20,164 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:18:20,169 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:18:20,171 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:18:20,173 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:18:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:19:20,054 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:19:20,058 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:19:20,067 - map_api - INFO - 找到 4 个目录
2025-08-05 11:19:20,068 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:19:20,073 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:19:20,075 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:19:20,084 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:19:20,086 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:19:20,092 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:19:20,095 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:19:20,104 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:19:20,107 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:19:20,117 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:19:20,120 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:19:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:20:20,075 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:20:20,080 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:20:20,093 - map_api - INFO - 找到 4 个目录
2025-08-05 11:20:20,096 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:20:20,103 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:20:20,105 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:20:20,113 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:20:20,118 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:20:20,126 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:20:20,127 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:20:20,156 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:20:20,160 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:20:20,161 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:20:20,164 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:20:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:21:20,053 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:21:20,062 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:21:20,073 - map_api - INFO - 找到 4 个目录
2025-08-05 11:21:20,082 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:21:20,088 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:21:20,093 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:21:20,124 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:21:20,131 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:21:20,138 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:21:20,143 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:21:20,152 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:21:20,153 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:21:20,162 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:21:20,164 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:21:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:22:20,082 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 11:22:20,088 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 11:22:20,100 - map_api - INFO - 找到 4 个目录
2025-08-05 11:22:20,102 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 11:22:20,107 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 11:22:20,114 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 11:22:20,125 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 11:22:20,129 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 11:22:20,136 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 11:22:20,139 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 11:22:20,152 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 11:22:20,154 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 11:22:20,160 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 11:22:20,163 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:22:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 11:23:18,619 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:23:18,651 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:23:18,738 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:23:18,748 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:23:18] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:23:18,756 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:23:18,759 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:23:18] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:23:18,854 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:23:18,864 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:23:18,865 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:23:18,869 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:23:18] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:23:18,910 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:23:18,915 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:23:18] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:23:20,173 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:23:20,179 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 11:23:20,190 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:23:20,197 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:23:20] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:23:20,208 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 11:23:20,213 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:23:20] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 11:23:21,473 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:23:21] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:23:21,546 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 11:23:21,583 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 11:23:21,622 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:23:21] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:25:58,471 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:25:58,485 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:25:58,486 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:25:58,489 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:25:58] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:25:58,499 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 11:25:58,515 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:25:58,517 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 11:25:58,527 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:25:58] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:25:58,530 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:25:58] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 11:25:58,539 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:25:58,556 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:25:58,566 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:25:58,570 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:25:58,585 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:25:58,588 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:25:58] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:25:58,592 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:25:58] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:25:58,599 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:25:58,607 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:25:58] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:25:59,996 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:25:59] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:26:00,125 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 11:26:00,174 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 11:26:00,198 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:26:00] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:26:28,716 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:26:28,731 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:26:28,733 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:26:28,738 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:26:28] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:26:28,744 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:26:28,754 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:26:28] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:26:28,790 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:26:28,797 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:26:28,805 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:26:28,808 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:26:28] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:26:28,810 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:26:28,819 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:26:28] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:26:30,327 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 11:26:30,339 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:26:30,373 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 11:26:30,377 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:26:30] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 11:26:30,390 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:26:30,404 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:26:30] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:26:30,813 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:26:30] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:26:30,824 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 11:26:30,860 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 11:26:30,862 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:26:30] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:28:32,058 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:28:32,070 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:28:32,073 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:28:32] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:28:32,807 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:28:32,815 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:28:32,828 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:28:32,830 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:28:32] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:28:32,848 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:28:32,856 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:28:32] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:30:54,129 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:30:54,139 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:30:54,146 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:30:54,152 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:30:54] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:30:54,155 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:30:54,167 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:30:54] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:30:54,165 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 11:30:54,181 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 11:30:54,185 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:30:54] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 11:30:54,236 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:30:54,383 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:30:54,386 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:30:54,403 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:30:54] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:30:54,407 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:30:54,419 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:30:54] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:30:54,432 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:30:54,439 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:30:54,447 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:30:54] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:30:55,899 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:30:55] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:30:55,979 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 11:30:56,048 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 11:30:56,115 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:30:56] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:34:10,560 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:34:10,583 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:34:10,631 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:34:10] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:34:10,600 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:34:10,641 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:34:10,643 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:34:10] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:34:10,743 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 11:34:11,021 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:34:11,086 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:34:11,119 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:34:11,149 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 11:34:11,158 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:34:11,187 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:34:11] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 11:34:11,165 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:34:11,191 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:34:11] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:34:11,177 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:34:11,207 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:34:11] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:34:11,209 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:34:11] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:34:12,555 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:34:12] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:34:12,953 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 11:34:13,051 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 11:34:13,123 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:34:13] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:46:00,387 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:46:00,401 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:46:00,514 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:46:00,520 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:46:00] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:46:00,551 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:46:00,637 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:46:00] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:46:00,775 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 11:46:00,788 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 11:46:00,791 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 11:46:00,798 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:46:00] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 11:46:01,091 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 11:46:01,107 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 11:46:01,213 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 11:46:01,215 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 11:46:01,219 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 11:46:01,227 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:46:01] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 11:46:01,229 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:46:01] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 11:46:01,230 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 11:46:01] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 11:46:02,586 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:46:02] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 11:46:02,595 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 11:46:02,639 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 11:46:02,643 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 11:46:02] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:04:27,484 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:04:27,539 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:04:27,562 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:04:27,595 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:04:27] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:04:27,631 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:04:27,640 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:04:27] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:04:27,671 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:04:27,677 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 12:04:27,686 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:04:27,689 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 12:04:27,692 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:04:27,706 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:04:27] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:04:27,708 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 12:04:27,710 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:04:27,711 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 12:04:27,732 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:04:27] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 12:04:27,734 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:04:27] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:04:27,736 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:04:27] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 12:04:29,180 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:04:29] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:04:29,197 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 12:04:29,295 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 12:04:29,353 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:04:29] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:15:25,778 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:15:25,813 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:15:25,901 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:15:25,942 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:15:25] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:15:25,946 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 12:15:26,012 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 12:15:26,039 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:15:26,048 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:15:26,054 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:15:26] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:15:26,050 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 12:15:26,062 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:15:26] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 12:15:26,089 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:15:26,093 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 12:15:26,094 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:15:26,103 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:15:26] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 12:15:26,104 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:15:26] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:15:26,109 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:15:26,119 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:15:26] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:15:27,487 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:15:27] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:15:27,512 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 12:15:27,605 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 12:15:27,666 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:15:27] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:18:39,744 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:18:39,905 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:18:40,357 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 12:18:40,363 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:18:40,613 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:18:40] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:18:40,488 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:18:40,615 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 12:18:40,621 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:18:40] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:18:40,631 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:18:40] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 12:18:40,825 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:18:40,837 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:18:40,911 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 12:18:40,922 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:18:40,929 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:18:40,941 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:18:40] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:18:40,949 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:18:40] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:18:40,952 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 12:18:40,966 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:18:40] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 12:18:42,504 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:18:42] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:18:42,668 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 12:18:42,767 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 12:18:42,883 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:18:42] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:19:49,617 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:19:49,631 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:19:49,639 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:19:49] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:19:49,649 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:19:49,695 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:19:49,717 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:19:49,721 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 12:19:49,758 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 12:19:51,759 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:19:51] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:19:51,776 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 12:19:51,779 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 12:19:51,785 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:19:51] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 12:19:51,781 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:19:51,788 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:19:51] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 12:19:51,784 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:19:51,800 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:19:51] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:19:51,815 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:19:51,821 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:19:51] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:19:53,290 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:19:53] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:19:53,307 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 12:19:53,404 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 12:19:53,418 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:19:53] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:20:49,154 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:20:49,167 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:20:49,171 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:20:49] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:20:49,175 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:20:49,187 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:20:49,192 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:20:49] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:20:49,215 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:20:49,224 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 12:20:49,250 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:20:49,258 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:20:49] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:20:49,253 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 12:20:49,266 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:20:49] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 12:20:49,292 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:20:49,366 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 12:20:49,407 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:20:49,436 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:20:49] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:20:49,426 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 12:20:49,439 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:20:49] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 12:20:50,706 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:20:50] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:20:50,749 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 12:20:50,788 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 12:20:50,838 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:20:50] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:24:07,850 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:24:07,852 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:24:07,866 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:24:07,869 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:24:07,880 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:24:07] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:24:07,877 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 12:24:07,882 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:24:07] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:24:07,896 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 12:24:07,899 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:24:07] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 12:24:07,907 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:24:07,938 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 12:24:07,939 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:24:07,940 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:24:07,946 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:24:07] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:24:07,950 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 12:24:07,959 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:24:07] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 12:24:07,982 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:24:07,987 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:24:07] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:24:09,485 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:24:09] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:24:09,582 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 12:24:09,724 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 12:24:09,747 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:24:09] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:27:30,580 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:27:30,645 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:27:30,667 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:27:30,672 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:27:30] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:27:30,683 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:27:30,689 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:27:30] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:27:30,698 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:27:30,706 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 12:27:30,712 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:27:30,724 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:27:30] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:27:30,730 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:27:30,740 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 12:27:30,760 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 12:27:30,761 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:27:30,767 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:27:30] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 12:27:30,769 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:27:30] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:27:30,772 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 12:27:30,774 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:27:30] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 12:27:32,262 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:27:32] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:27:32,305 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 12:27:32,428 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 12:27:32,469 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:27:32] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:28:12,951 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:28:12,975 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:28:12,984 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:28:13,073 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:28:13] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:28:12,993 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:28:13,076 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 12:28:13,087 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:28:13] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:28:13,107 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 12:28:13,111 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:28:13] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 12:28:13,160 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:28:13,197 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:28:13,210 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 12:28:13,213 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:28:13,216 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:28:13,253 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:28:13] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:28:13,249 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 12:28:13,254 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:28:13] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:28:13,263 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:28:13] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 12:28:14,683 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:28:14] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:28:14,720 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 12:28:14,829 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 12:28:14,856 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:28:14] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:34:36,468 - map_api - INFO - 请求文件: baseMap.json
2025-08-05 12:34:36,528 - map_api - INFO - 成功获取文件 baseMap.json, 内容大小: 3253 字节
2025-08-05 12:34:36,533 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:34:36] "GET /api/map/base-map HTTP/1.1" 200 -
2025-08-05 12:34:38,819 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:34:38,864 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:34:38,884 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:34:38,887 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:34:38] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:34:38,917 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:34:38,924 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 12:34:38,968 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 12:34:39,222 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:34:39,223 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:34:39,229 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:34:39] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:34:39,230 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:34:39] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:34:39,234 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:34:39,337 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 12:34:39,341 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:34:39] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 12:34:39,345 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 12:34:39,346 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:34:39,349 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:34:39] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 12:34:39,350 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:34:39] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:34:40,429 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:34:40] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:34:40,436 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 12:34:40,521 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 12:34:40,524 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:34:40] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:37:22,662 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:37:22,706 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:37:22,710 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:37:22] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:37:22,712 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:37:22,729 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:37:22,733 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:37:22] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:37:22,776 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 12:37:22,779 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 12:37:22,789 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 12:37:22,963 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:37:22] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 12:37:22,791 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 12:37:22,849 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 12:37:22,970 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:37:22] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 12:37:22,978 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 12:37:22,982 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:37:22] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 12:37:23,028 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 12:37:23,112 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 12:37:23,164 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 12:37:23] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 12:37:24,628 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:37:24] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 12:37:24,717 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 12:37:24,780 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 12:37:24,787 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 12:37:24] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:21:24,711 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:21:24] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-08-05 15:21:24,718 - root - INFO - 获取图层 test_myworkspace:5-1584 的信息
2025-08-05 15:21:24,833 - root - INFO - 成功获取图层 test_myworkspace:5-1584 的边界框信息
2025-08-05 15:21:24,834 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:21:24] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=5-1584 HTTP/1.1" 200 -
2025-08-05 15:21:28,029 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:21:28,075 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:21:28,082 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:21:28] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:21:28,092 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:21:28,129 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:21:28,132 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:21:28] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:21:28,189 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:21:28,201 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 15:21:28,204 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:21:28,212 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:21:28] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:21:28,234 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 15:21:28,245 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:21:28] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 15:21:29,603 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:21:29,613 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 15:21:29,614 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:21:29,620 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:21:29] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:21:29,625 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 15:21:29,629 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:21:29] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 15:21:29,802 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:21:29] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:21:29,809 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 15:21:29,841 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 15:21:29,843 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:21:29] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:30:50,843 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:30:50,860 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:30:50,879 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:30:50] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:30:51,021 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:30:51,035 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:30:51,066 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:30:51] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:30:51,065 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:30:51,098 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 15:30:51,099 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 15:30:51,109 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:30:51,110 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 15:30:51,124 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:30:51] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:30:51,122 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 15:30:51,126 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:30:51] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 15:30:51,128 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:30:51] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 15:30:51,257 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:30:51,305 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:30:51,336 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:30:51] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:30:52,715 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:30:52] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:30:52,726 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 15:30:52,780 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 15:30:52,789 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:30:52] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:35:17,450 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:35:17,579 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:35:17,582 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:35:17,617 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:35:17] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:35:17,602 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 15:35:17,619 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:35:17,660 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:35:17] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:35:17,665 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 15:35:17,673 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:35:17] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 15:35:17,720 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:35:17,746 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 15:35:17,760 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:35:17,772 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:35:17] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:35:17,792 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 15:35:17,800 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:35:17] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 15:35:17,797 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:35:17,835 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:35:17,839 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:35:17] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:35:19,243 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:35:19] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:35:19,268 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 15:35:19,368 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 15:35:19,415 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:35:19] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:37:34,058 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:37:34,129 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:37:34,136 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:37:34,174 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:37:34] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:37:34,172 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:37:34,219 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:37:34] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:37:34,293 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 15:37:34,306 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:37:34,321 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 15:37:34,332 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 15:37:34,353 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:37:34] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 15:37:34,337 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:37:34,344 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:37:34,390 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:37:34] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:37:34,357 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 15:37:34,424 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:37:34] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 15:37:34,412 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:37:34,429 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:37:34] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:37:36,359 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:37:36] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:37:36,424 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 15:37:36,477 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 15:37:36,481 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:37:36] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:47:31,732 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:47:31,801 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:47:31,808 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:47:31,830 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:47:31] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:47:31,817 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:47:31,847 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:47:31] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:47:32,127 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:47:32,196 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 15:47:32,269 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:47:32,295 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:47:32,306 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:47:32] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:47:32,307 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 15:47:32,311 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:47:32] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 15:47:32,314 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:47:32,330 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:47:32] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:47:32,315 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 15:47:32,347 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 15:47:32,354 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:47:32] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 15:47:33,734 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:47:33] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:47:33,865 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 15:47:33,938 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 15:47:34,014 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:47:34] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:48:08,885 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:48:08,901 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:48:08,910 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:48:08] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:48:08,904 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:48:08,936 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:48:08,950 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:48:08] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:48:08,954 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:48:08,954 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 15:48:08,976 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:48:08,977 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 15:48:08,981 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:48:08] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:48:08,983 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:48:08] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 15:48:09,029 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:48:09,035 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 15:48:09,042 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:48:09,046 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:48:09] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:48:09,049 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 15:48:09,059 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:48:09] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 15:48:10,639 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:48:10] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:48:10,749 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 15:48:10,815 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 15:48:10,823 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:48:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:49:45,404 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:49:45,420 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:49:45,424 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:49:45] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:49:45,513 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:49:45,524 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:49:45,529 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 15:49:45,539 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:49:45,544 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:49:45,562 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:49:45] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:49:45,550 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 15:49:45,582 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:49:45] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:49:45,627 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:49:45] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 15:49:45,630 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 15:49:45,639 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 15:49:45,655 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:49:45] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 15:49:45,952 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:49:45,964 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:49:46,005 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:49:46] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:49:47,445 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:49:47] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:49:47,458 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 15:49:47,494 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 15:49:47,496 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:49:47] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:50:39,675 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:50:39,709 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:50:39,725 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:50:39] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:50:39,714 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:50:39,727 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 15:50:39,768 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 15:50:39,794 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:50:39] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 15:50:39,778 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:50:39,854 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:50:39] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:50:39,847 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 15:50:39,876 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 15:50:39,878 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 15:50:39,910 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:50:39] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 15:50:39,907 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 15:50:39,941 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:50:39] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 15:50:39,960 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 15:50:39,974 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 15:50:39,985 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 15:50:39] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 15:50:41,518 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:50:41] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 15:50:41,641 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 15:50:41,696 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 15:50:41,699 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 15:50:41] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:00:00,144 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:00:00,164 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:00:00,248 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:00:00,258 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:00:00] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:00:00,261 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:00:00,286 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:00:00,298 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:00:00] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:00:00,301 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:00:00,304 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:00:00] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:00:00,581 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:00:00,606 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:00:00,610 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:00:00] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:00:00,608 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:00:00,613 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:00:00,640 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:00:00,641 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:00:00,651 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:00:00] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:00:00,653 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:00:00] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:00:02,174 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:00:02] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:00:02,232 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:00:02,353 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:00:02,356 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:00:02] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:01:35,183 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:01:35,214 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:01:35,219 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:01:35] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:01:35,234 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:01:35,265 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:01:35,268 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:01:35] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:01:35,346 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:01:35,369 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:01:35,394 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:01:35] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:01:35,387 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:01:35,415 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:01:35,419 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:01:35] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:01:35,466 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:01:35,644 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:01:35,726 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:01:35,776 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:01:35,789 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:01:35] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:01:35,793 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:01:35] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:01:37,028 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:01:37] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:01:37,106 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:01:37,154 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:01:37,170 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:01:37] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:02:09,408 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:02:09,431 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:02:09,447 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:02:09,460 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:02:09] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:02:09,449 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:02:09,463 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:02:09] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:02:09,540 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:02:09,613 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:02:09,720 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:02:09,806 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:02:09] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:02:09,812 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:02:09,820 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:02:09,848 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:02:09,854 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:02:09] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:02:09,863 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:02:09] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:02:09,897 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:02:09,984 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:02:10,081 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:02:10] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:02:10,980 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:02:10] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:02:11,344 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:02:11,447 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:02:11,468 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:02:11] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:06:19,058 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:06:19,070 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:06:19,076 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:06:19,082 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:06:19,086 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:06:19,156 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:06:19,160 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:06:19] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:06:19,163 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:06:19] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:06:19,164 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:06:19] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:06:19,333 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:06:19,394 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:06:19,401 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:06:19,404 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:06:19,419 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:06:19] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:06:19,422 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:06:19,424 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:06:19,432 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:06:19] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:06:19,438 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:06:19] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:06:21,074 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:06:21] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:06:21,276 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:06:21,351 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:06:21,370 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:06:21] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:07:18,463 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:07:18,479 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:07:18,493 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:07:18,494 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:07:18,498 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:07:18] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:07:18,503 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:07:18] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:07:18,501 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:07:18,552 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:07:18,558 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:07:18] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:07:18,637 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:07:18,649 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:07:18,663 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:07:18,664 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:07:18,680 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:07:18] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:07:18,675 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:07:18,698 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:07:18] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:07:18,689 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:07:18,710 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:07:18] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:07:20,203 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:07:20] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:07:20,248 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:07:20,343 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:07:20,349 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:07:20] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:11:49,549 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:11:49,641 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:11:49,644 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:11:49,660 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:11:49,662 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:11:49,670 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:11:49] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:11:49,664 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:11:49,673 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:11:49] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:11:49,674 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:11:49] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:11:49,728 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:11:49,839 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:11:49,847 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:11:49,852 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:11:49,882 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:11:49,891 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:11:49,905 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:11:49] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:11:49,909 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:11:49] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:11:49,918 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:11:49] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:11:50,744 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:11:50] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:11:50,753 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:11:50,838 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:11:50,840 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:11:50] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:12:25,671 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:12:25,737 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:12:25,749 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:12:25,754 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:12:25,757 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:12:25,765 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:12:25] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:12:25,766 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:12:25] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:12:25,773 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:12:25,799 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:12:25] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:12:25,923 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:12:25,990 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:12:26,026 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:12:26,105 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:12:26] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:12:26,026 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:12:26,086 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:12:26,115 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:12:26] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:12:26,118 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:12:26,121 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:12:26] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:12:27,280 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:12:27] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:12:27,510 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:12:27,567 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:12:27,590 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:12:27] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:17:01,093 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:17:01,096 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:17:01,104 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:17:01,106 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:17:01] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:17:01,107 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:17:01,109 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:17:01] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:17:38,914 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:17:38,924 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:17:38,931 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:17:38,938 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:17:38] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:17:38,947 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:17:38,952 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:17:38] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:23:20,355 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:23:20,368 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:23:20,370 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:23:20] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:23:20,391 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:23:20,398 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:23:20,401 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:23:20] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:23:20,428 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:23:20,492 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:23:20,502 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:23:20,528 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:23:20] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:23:20,504 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:23:20,511 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:23:20,545 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:23:20] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:23:20,549 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:23:20,551 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:23:20] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:23:20,556 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:23:20,572 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:23:20,574 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:23:20] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:23:22,154 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:23:22] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:23:22,371 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:23:22,514 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:23:22,532 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:23:22] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:27:40,928 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:27:40,935 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:27:40,970 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:27:40,980 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:27:40] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:27:40,988 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:27:40,990 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:27:40] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:30:08,910 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:30:08,926 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:30:08,927 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:30:08,936 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:30:08] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:30:08,940 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:30:08,947 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:30:08] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:32:52,456 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:32:52,492 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:32:52,514 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:32:52,525 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:32:52] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:32:52,543 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:32:52,547 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:32:52] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:32:52,766 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:32:52,836 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:32:52,839 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:32:52,844 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:32:52,848 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:32:52,858 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:32:52] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:32:52,863 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:32:52,864 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:32:52,871 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:32:52] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:32:52,866 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:32:52,874 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:32:52] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:32:52,882 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:32:52] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:32:54,207 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:32:54] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:32:54,237 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:32:54,445 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:32:54,478 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:32:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:39:15,780 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:39:15,785 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:39:15,809 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:39:15,810 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:39:15] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:39:15,826 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:39:15,827 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:39:15] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:39:35,245 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:39:35,250 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:39:35,257 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:39:35,259 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:39:35,263 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:39:35] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:39:35,264 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:39:35] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:41:56,943 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:41:56,955 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:41:56,958 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:41:56] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:41:56,981 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:41:57,028 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:41:57,040 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:41:57] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:41:57,063 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:41:57,071 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:41:57,085 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:41:57,086 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:41:57,091 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:41:57] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:41:57,097 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:41:57] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:41:57,880 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:41:57,894 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:41:57,900 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:41:57] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:41:57,925 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:41:57,938 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:41:57,944 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:41:57] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:41:59,013 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:41:59] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:41:59,055 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:41:59,275 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:41:59,291 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:41:59] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:47:22,396 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:47:22,523 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:47:22,525 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:47:22,541 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:47:22] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:47:22,543 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:47:22,551 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:47:22,560 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:47:22,568 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:47:22] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:47:22,573 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:47:22] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:47:22,579 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:47:22,595 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:47:22,600 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:47:22] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:47:23,285 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:47:23,487 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:47:23,631 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:47:23,632 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:47:23,642 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:47:23] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:47:23,674 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:47:23] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:47:23,712 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:47:23] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:47:23,732 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:47:23,907 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:47:24,048 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:47:24] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:49:24,898 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:49:24,992 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:49:25,014 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 16:49:25,099 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:49:25,156 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:49:25] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:49:25,117 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:49:25,119 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 16:49:25,180 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:49:25] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:49:25,181 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:49:25] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 16:49:25,411 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 16:49:25,577 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 16:49:25,582 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:49:25] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 16:49:25,649 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 16:49:25,798 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 16:49:26,341 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:49:26] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 16:49:26,145 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 16:49:26,356 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:49:26] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 16:49:26,371 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 16:49:26,375 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 16:49:26] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 16:49:26,380 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 16:49:26,417 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 16:49:26,446 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 16:49:26] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:08:07,072 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:08:07,135 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:08:07,143 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:08:07,170 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:08:07,183 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:08:07] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:08:07,188 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:08:07,192 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:08:07,281 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:08:07] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:08:07,286 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:08:07] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:08:07,360 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:08:07,376 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:08:07,380 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:08:07] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:08:07,382 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:08:07,394 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:08:07,410 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:08:07,413 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:08:07] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:08:07,430 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:08:07,435 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:08:07] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:08:09,431 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:08:09] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:08:10,009 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:08:10,114 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:08:10,149 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:08:10] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:13:18,484 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:13:18,486 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:13:18,505 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:13:18,520 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:13:18] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:13:18,519 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:13:18,538 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:13:18] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:13:28,241 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:13:28,253 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:13:28,259 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:13:28,280 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:13:28] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:13:28,283 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:13:28,315 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:13:28] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:13:28,343 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:13:28,346 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:13:28,394 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:13:28,398 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:13:28,401 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:13:28,423 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:13:28] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:13:28,416 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:13:28,426 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:13:28] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:13:28,421 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:13:28,452 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:13:28] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:13:28,454 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:13:28,469 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:13:28] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:13:29,738 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:13:29] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:13:29,762 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:13:29,948 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:13:29,952 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:13:29] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:14:39,261 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:14:39,271 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:14:39,277 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:14:39,281 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:14:39,292 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:14:39] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:14:39,295 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:14:39,298 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:14:39,306 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:14:39] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:14:39,307 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:14:39] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:14:39,326 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:14:39,334 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:14:39,337 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:14:39] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:14:39,403 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:14:39,458 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:14:39,466 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:14:39,493 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:14:39] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:14:39,472 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:14:39,505 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:14:39] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:14:40,729 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:14:40] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:14:40,739 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:14:40,782 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:14:40,785 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:14:40] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:19:44,877 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:19:45,030 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:19:45,060 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:19:45,087 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:19:45,116 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:19:45,134 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:19:45] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:19:45,130 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:19:45,138 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:19:45] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:19:45,146 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:19:45] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:19:45,280 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:19:45,313 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:19:45,332 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:19:45,334 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:19:45,342 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:19:45] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:19:45,347 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:19:45,347 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:19:45,352 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:19:45] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:19:45,353 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:19:45] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:19:47,105 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:19:47] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:19:47,251 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:19:47,482 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:19:47,529 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:19:47] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:24:49,712 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:24:49,761 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:24:49,790 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:24:49,805 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:24:49,809 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:24:49,818 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:24:49] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:24:49,827 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:24:49] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:24:49,815 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:24:49,831 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:24:49] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:24:49,916 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:24:49,949 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:24:49,951 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:24:49,975 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:24:49,980 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:24:49,994 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:24:49] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:24:49,998 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:24:49] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:24:49,996 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:24:50,008 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:24:50] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:24:51,714 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:24:51] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:24:51,766 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:24:51,875 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:24:51,885 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:24:51] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:30:41,918 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:30:41,959 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:30:41,965 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:30:41] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:30:42,106 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:30:42,144 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:30:42,157 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:30:42] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:30:42,355 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:30:42,366 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:30:42,369 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:30:42,385 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:30:42] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:30:42,395 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:30:42,399 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:30:42] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:30:43,402 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:30:43,408 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:30:43,449 - map_api - INFO - 找到 4 个目录
2025-08-05 17:30:43,458 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:30:43,490 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:30:43,497 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:30:43,559 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:30:43,564 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:30:43,631 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:30:43,637 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:30:43,696 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:30:43,702 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:30:43,704 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:30:43,708 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:30:43] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:30:44,589 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:30:44,605 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:30:44,619 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:30:44] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:30:44,635 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:30:44,647 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:30:44,649 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:30:44] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:30:45,498 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:30:45] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:30:45,510 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:30:45,596 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:30:45,597 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:30:45] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:31:44,546 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:31:44,564 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:31:44,578 - map_api - INFO - 找到 4 个目录
2025-08-05 17:31:44,581 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:31:44,591 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:31:44,595 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:31:44,621 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:31:44,628 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:31:44,637 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:31:44,640 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:31:44,654 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:31:44,656 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:31:44,663 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:31:44,672 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:31:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:32:44,382 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:32:44,388 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:32:44,412 - map_api - INFO - 找到 4 个目录
2025-08-05 17:32:44,415 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:32:44,424 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:32:44,426 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:32:44,436 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:32:44,441 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:32:44,450 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:32:44,451 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:32:44,463 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:32:44,465 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:32:44,467 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:32:44,475 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:32:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:33:44,376 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:33:44,380 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:33:44,392 - map_api - INFO - 找到 4 个目录
2025-08-05 17:33:44,393 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:33:44,397 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:33:44,401 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:33:44,433 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:33:44,443 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:33:44,450 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:33:44,452 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:33:44,459 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:33:44,460 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:33:44,462 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:33:44,471 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:33:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:34:44,371 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:34:44,375 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:34:44,385 - map_api - INFO - 找到 4 个目录
2025-08-05 17:34:44,387 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:34:44,393 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:34:44,395 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:34:44,401 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:34:44,406 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:34:44,410 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:34:44,411 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:34:44,419 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:34:44,421 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:34:44,422 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:34:44,424 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:34:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:35:44,376 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:35:44,379 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:35:44,390 - map_api - INFO - 找到 4 个目录
2025-08-05 17:35:44,392 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:35:44,396 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:35:44,398 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:35:44,409 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:35:44,410 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:35:44,431 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:35:44,435 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:35:44,441 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:35:44,442 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:35:44,445 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:35:44,454 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:35:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:36:44,373 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:36:44,377 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:36:44,389 - map_api - INFO - 找到 4 个目录
2025-08-05 17:36:44,390 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:36:44,418 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:36:44,422 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:36:44,431 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:36:44,437 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:36:44,446 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:36:44,447 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:36:44,460 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:36:44,461 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:36:44,462 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:36:44,473 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:36:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:38:20,386 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:38:20,391 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:38:20,418 - map_api - INFO - 找到 4 个目录
2025-08-05 17:38:20,422 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:38:20,427 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:38:20,428 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:38:20,434 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:38:20,436 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:38:20,462 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:38:20,468 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:38:20,474 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:38:20,475 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:38:20,476 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:38:20,486 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:38:20] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:38:43,403 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:38:43,408 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:38:43,422 - map_api - INFO - 找到 4 个目录
2025-08-05 17:38:43,424 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:38:43,434 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:38:43,436 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:38:43,444 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:38:43,449 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:38:43,458 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:38:43,460 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:38:43,473 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:38:43,479 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:38:43,480 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:38:43,483 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:38:43] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:38:52,886 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:38:52,929 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:38:52,936 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:38:52,949 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:38:52,952 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:38:52,953 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:38:52,962 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:38:52] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:38:52,964 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:38:52] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:38:52,967 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:38:52] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:38:53,009 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:38:53,023 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:38:53,032 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:38:53] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:38:53,107 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:38:53,108 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:38:53,138 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:38:53,143 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:38:53] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:38:53,141 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:38:53,147 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:38:53] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:38:54,588 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:38:54] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:38:54,606 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:38:54,690 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:38:54,692 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:38:54] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:39:44,374 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:39:44,379 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:39:44,386 - map_api - INFO - 找到 4 个目录
2025-08-05 17:39:44,388 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:39:44,417 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:39:44,421 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:39:44,425 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:39:44,426 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:39:44,449 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:39:44,454 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:39:44,458 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:39:44,459 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:39:44,460 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:39:44,466 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:39:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:40:48,158 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:40:48,169 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:40:48,181 - map_api - INFO - 找到 4 个目录
2025-08-05 17:40:48,184 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:40:48,210 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:40:48,213 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:40:48,229 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:40:48,239 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:40:48,245 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:40:48,248 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:40:48,259 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:40:48,266 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:40:48,270 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:40:48,272 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:40:48] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:43:23,768 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:43:23,796 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:43:23,809 - map_api - INFO - 找到 4 个目录
2025-08-05 17:43:23,812 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:43:23,819 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:43:23,826 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:43:23,834 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:43:23,844 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:43:23,855 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:43:23,859 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:43:23,867 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:43:23,869 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:43:23,875 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:43:23,884 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:43:23] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:45:13,046 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:45:13,155 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:45:13,271 - map_api - INFO - 找到 4 个目录
2025-08-05 17:45:13,426 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:45:13,665 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:45:13,748 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:45:13,877 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:45:13,977 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:45:14,151 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:45:14,322 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:45:14,411 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:45:14,437 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:45:14,459 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:45:14,504 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:45:14] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:45:46,123 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:45:46,151 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:45:46,189 - map_api - INFO - 找到 4 个目录
2025-08-05 17:45:46,203 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:45:46,217 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:45:46,224 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:45:46,241 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:45:46,247 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:45:46,269 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:45:46,272 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:45:46,292 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:45:46,294 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:45:46,295 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:45:46,304 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:45:46] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:47:09,525 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:47:09,536 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:47:09,577 - map_api - INFO - 找到 4 个目录
2025-08-05 17:47:09,585 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:47:09,609 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:47:09,618 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:47:09,635 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:47:09,639 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:47:09,668 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:47:09,695 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:47:09,711 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:47:09,719 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:47:09,721 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:47:09,723 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:47:09] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:47:44,514 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:47:44,525 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:47:44,540 - map_api - INFO - 找到 4 个目录
2025-08-05 17:47:44,549 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:47:44,557 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:47:44,564 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:47:44,575 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:47:44,583 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:47:44,594 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:47:44,595 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:47:44,604 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:47:44,607 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:47:44,609 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:47:44,613 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:47:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:47:46,105 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:47:46,115 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:47:46,119 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:47:46] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:47:46,159 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:47:46,210 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:47:46,258 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:47:46,271 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:47:46] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:47:46,264 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:47:46,295 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:47:46,344 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:47:46] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:47:46,323 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:47:46,347 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:47:46,365 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:47:46] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:47:46,383 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:47:46,391 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:47:46] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:47:46,423 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:47:46,481 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:47:46,528 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:47:46] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:47:48,063 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:47:48] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:47:48,191 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:47:48,280 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:47:48,326 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:47:48] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:48:44,380 - map_api - INFO - 开始获取ODM任务列表
2025-08-05 17:48:44,385 - map_api - INFO - 请求目录列表: ODM/Input
2025-08-05 17:48:44,397 - map_api - INFO - 找到 4 个目录
2025-08-05 17:48:44,400 - map_api - INFO - 请求文件: ODM/Input/20250705171599/TaskInfo.json
2025-08-05 17:48:44,406 - map_api - ERROR - 获取文件失败: ODM/Input/20250705171599/TaskInfo.json, 状态码: 404
2025-08-05 17:48:44,415 - map_api - INFO - 请求文件: ODM/Input/20250705171600/TaskInfo.json
2025-08-05 17:48:44,421 - map_api - INFO - 成功获取文件 ODM/Input/20250705171600/TaskInfo.json, 内容大小: 2514 字节
2025-08-05 17:48:44,423 - map_api - INFO - 请求文件: ODM/Input/20250705171601/TaskInfo.json
2025-08-05 17:48:44,429 - map_api - INFO - 成功获取文件 ODM/Input/20250705171601/TaskInfo.json, 内容大小: 2384 字节
2025-08-05 17:48:44,431 - map_api - INFO - 请求文件: ODM/Input/20250705171602/TaskInfo.json
2025-08-05 17:48:44,437 - map_api - INFO - 成功获取文件 ODM/Input/20250705171602/TaskInfo.json, 内容大小: 2611 字节
2025-08-05 17:48:44,444 - map_api - INFO - 获取到 4 个任务信息
2025-08-05 17:48:44,446 - map_api - INFO - 任务排序结果: 运行中(0) > 未开始(1) > 已完成(3)
2025-08-05 17:48:44,450 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:48:44] "GET /api/map/odm/tasks HTTP/1.1" 200 -
2025-08-05 17:49:15,917 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:49:16,389 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:49:16,573 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:49:16,725 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:49:17,772 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:49:18,877 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:49:18] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:49:17,951 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:49:18,882 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:49:18] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:49:18,886 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:49:18] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:49:18,999 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:49:19,207 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:49:19,374 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:49:19] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:49:26,303 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:49:26] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:49:26,070 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:49:26,287 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:49:26,819 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:49:26,883 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:49:26,888 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:49:26] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:49:26,890 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:49:26] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:49:26,961 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:49:27,213 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:49:27,272 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:49:27] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:51:29,721 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:51:29,726 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:51:29,898 - map_api - INFO - 请求文件: baseStyle2.json
2025-08-05 17:51:29,915 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:51:29,924 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:51:29] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:51:29,945 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:51:29,959 - map_api - INFO - 成功获取文件 baseStyle2.json, 内容大小: 2818 字节
2025-08-05 17:51:29,974 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:51:29] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:51:29,981 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:51:29] "GET /api/map/base-style2 HTTP/1.1" 200 -
2025-08-05 17:51:29,990 - map_api - INFO - 请求文件: baseMap2.json
2025-08-05 17:51:30,008 - map_api - INFO - 成功获取文件 baseMap2.json, 内容大小: 6332 字节
2025-08-05 17:51:30,013 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:51:30] "GET /api/map/base-map2 HTTP/1.1" 200 -
2025-08-05 17:51:30,092 - map_api - INFO - 请求文件: baseMap3.json
2025-08-05 17:51:30,104 - map_api - INFO - 请求文件: baseStyle3.json
2025-08-05 17:51:30,154 - map_api - INFO - 成功获取文件 baseStyle3.json, 内容大小: 2818 字节
2025-08-05 17:51:30,160 - map_api - INFO - 成功获取文件 baseMap3.json, 内容大小: 5080 字节
2025-08-05 17:51:30,261 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:51:30] "GET /api/map/base-style3 HTTP/1.1" 200 -
2025-08-05 17:51:30,275 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:51:30] "GET /api/map/base-map3 HTTP/1.1" 200 -
2025-08-05 17:51:31,821 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:51:31] "OPTIONS /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:51:31,874 - root - INFO - 获取图层 test_myworkspace:nanning 的信息
2025-08-05 17:51:31,938 - root - INFO - 成功获取图层 test_myworkspace:nanning 的边界框信息
2025-08-05 17:51:31,940 - werkzeug - INFO - 192.168.43.148 - - [05/Aug/2025 17:51:31] "GET /api/management/layers/bbox?workspace=test_myworkspace&layer=nanning HTTP/1.1" 200 -
2025-08-05 17:51:59,997 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:51:59] "[33mGET /api/query/?lat=30.123&lon=114.456&workspace=test HTTP/1.1[0m" 404 -
2025-08-05 17:52:02,443 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:52:02] "[33mGET /api/query/?lat=30.123&lon=114.456&workspace=test HTTP/1.1[0m" 404 -
2025-08-05 17:52:07,596 - root - INFO - 在工作区 'test' 中找到 1 个栅格图层
2025-08-05 17:52:07,597 - root - INFO - 在坐标点 (30.123, 114.456) 找到 0 个有效图层
2025-08-05 17:52:07,602 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:52:07] "GET /api/query?lat=30.123&lon=114.456&workspace=test HTTP/1.1" 200 -
2025-08-05 17:52:46,532 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-08-05 17:52:46,534 - root - INFO - 在坐标点 (30.123, 114.456) 找到 0 个有效图层
2025-08-05 17:52:46,538 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:52:46] "GET /api/query?lat=30.123&lon=114.456&workspace=test_myworkspace HTTP/1.1" 200 -
2025-08-05 17:53:17,136 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-08-05 17:53:17,484 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-08-05 17:53:17,580 - root - INFO - 图层 'nanning' 在坐标点 (22.89431, 108.22151) 有有效数据
2025-08-05 17:53:17,946 - root - INFO - 在工作区 'test_myworkspace' 中找到 11 个栅格图层
2025-08-05 17:53:17,979 - root - INFO - 图层 'id1' 在坐标点 (22.89431, 108.22151) 有有效数据
2025-08-05 17:53:17,979 - root - INFO - 在坐标点 (22.89431, 108.22151) 找到 2 个有效图层
2025-08-05 17:53:17,981 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:53:17] "GET /api/query?lat=22.89431&lon=108.22151&workspace=test_myworkspace HTTP/1.1" 200 -
2025-08-05 17:53:22,316 - werkzeug - INFO - 127.0.0.1 - - [05/Aug/2025 17:53:22] "GET /api/geo/status?task_id=5968c6d7-4bfc-4a5b-b525-31850241a34f HTTP/1.1" 200 -
