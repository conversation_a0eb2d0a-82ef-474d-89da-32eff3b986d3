#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer REST API服务 - 提供RESTful API访问GeoServer服务
"""

import os
import sys
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
import json

# 导入核心模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from core.manager import GeoServerManager, logger
from core.raster_query import GeoServerRasterQuery

# 导入API模块
from .management_api import management_api
from .batch_api import batch_api
from .tif_api import tif_api
from .geo_api import geo_api
from .map_api import map_api

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用跨域请求支持

# 注册API蓝图
app.register_blueprint(management_api, url_prefix='/api/management')
app.register_blueprint(batch_api, url_prefix='/api/batch')
app.register_blueprint(tif_api, url_prefix='/api/tif')
app.register_blueprint(geo_api, url_prefix='/api/geo')
app.register_blueprint(map_api, url_prefix='/api/map')

# 初始化查询和管理器实例
manager = GeoServerManager()
query = GeoServerRasterQuery(manager)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'ok',
        'message': 'GeoServer REST API服务正在运行'
    })



@app.route('/api/query', methods=['GET'])
def query_coordinate():
    """
    查询坐标点处的栅格图层
    
    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)
    
    返回:
        包含该坐标点有效数据的图层信息列表
    """
    try:
        # 获取查询参数
        lat_str = request.args.get('lat')
        lon_str = request.args.get('lon')
        workspace = request.args.get('workspace')
        
        # 验证必要参数
        if not all([lat_str, lon_str, workspace]):
            missing_params = []
            if not lat_str:
                missing_params.append('lat')
            if not lon_str:
                missing_params.append('lon')
            if not workspace:
                missing_params.append('workspace')
                
            return jsonify({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }), 400
        
        # 转换经纬度为浮点数
        try:
            lat = float(lat_str)
            lon = float(lon_str)
        except (ValueError, TypeError):
            return jsonify({
                'status': 'error',
                'message': '经纬度必须是有效的数值'
            }), 400
        
        # 执行查询
        layers = query.query_point(lat, lon, workspace)
        
        return jsonify({
            'status': 'success',
            'count': len(layers),
            'layers': layers
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/api/layers', methods=['GET'])
def get_layers():
    """
    获取工作区中的所有栅格图层
    
    查询参数:
        workspace: 工作区名称
        
    返回:
        工作区中的栅格图层列表
    """
    try:
        workspace = request.args.get('workspace')
        if not workspace:
            return jsonify({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }), 400
        
        # 获取图层列表
        layers = query.get_raster_layers(workspace)
        
        return jsonify({
            'status': 'success',
            'count': len(layers),
            'layers': layers
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/api/all_layers', methods=['GET'])
def get_all_layers():
    """
    获取工作区中的所有图层（包括栅格和矢量）
    
    查询参数:
        workspace: 工作区名称
        
    返回:
        工作区中的所有图层列表
    """
    try:
        workspace = request.args.get('workspace')
        if not workspace:
            return jsonify({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }), 400
        
        # 获取图层列表
        layers = query.get_all_layers(workspace)
        
        return jsonify({
            'status': 'success',
            'count': len(layers),
            'layers': layers
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/api/test_point', methods=['GET'])
def test_point():
    """
    测试坐标点在特定图层中是否有有效数据
    
    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)
        layer: 图层名称 (字符串，可选)
        
    返回:
        详细的测试结果
    """
    try:
        # 获取查询参数
        lat_str = request.args.get('lat')
        lon_str = request.args.get('lon')
        workspace = request.args.get('workspace')
        layer_name = request.args.get('layer')
        
        # 验证必要参数
        if not all([lat_str, lon_str, workspace]):
            missing_params = []
            if not lat_str:
                missing_params.append('lat')
            if not lon_str:
                missing_params.append('lon')
            if not workspace:
                missing_params.append('workspace')
                
            return jsonify({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }), 400
        
        # 转换经纬度为浮点数
        try:
            lat = float(lat_str)
            lon = float(lon_str)
        except (ValueError, TypeError):
            return jsonify({
                'status': 'error',
                'message': '经纬度必须是有效的数值'
            }), 400
        
        # 获取工作区中的所有栅格图层
        layers = query.get_raster_layers(workspace)
        
        if not layers:
            return jsonify({
                'status': 'error',
                'message': f'工作区 {workspace} 中没有找到栅格图层'
            }), 404
        
        results = []
        
        # 如果指定了特定图层，只测试该图层
        if layer_name:
            target_layers = [layer for layer in layers if layer['name'] == layer_name]
            if not target_layers:
                return jsonify({
                    'status': 'error',
                    'message': f'在工作区 {workspace} 中未找到图层 {layer_name}'
                }), 404
        else:
            target_layers = layers
        
        # 测试每个图层
        for layer in target_layers:
            layer_info = {
                'name': layer['name'],
                'store': layer['store'],
                'workspace': layer['workspace'],
                'id': layer['id']
            }
            
            # 检查边界框
            bbox = layer.get('bbox', {})
            if bbox:
                try:
                    minx = float(bbox.get('minx', 0))
                    miny = float(bbox.get('miny', 0))
                    maxx = float(bbox.get('maxx', 0))
                    maxy = float(bbox.get('maxy', 0))
                    
                    is_in_bbox = (minx <= lon <= maxx and miny <= lat <= maxy)
                    layer_info['bbox'] = {
                        'minx': minx,
                        'miny': miny,
                        'maxx': maxx,
                        'maxy': maxy,
                        'point_inside': is_in_bbox
                    }
                    
                    if is_in_bbox:
                        # 测试点是否有有效数据
                        has_data = query._check_point_data(lat, lon, workspace, layer['name'])
                        layer_info['has_data'] = has_data
                    else:
                        layer_info['has_data'] = False
                        layer_info['reason'] = '坐标点不在图层边界框内'
                except (ValueError, TypeError) as e:
                    layer_info['error'] = f'处理边界框时出错: {str(e)}'
            else:
                layer_info['error'] = '图层没有边界框信息'
            
            results.append(layer_info)
        
        return jsonify({
            'status': 'success',
            'coordinates': {
                'lat': lat,
                'lon': lon
            },
            'count': len(results),
            'results': results
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/api/pixel_value', methods=['GET'])
def get_pixel_value():
    """
    获取指定坐标点在特定图层中的像素值
    
    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)
        layer: 图层名称 (字符串)
        
    返回:
        像素值信息
    """
    try:
        # 获取查询参数
        lat_str = request.args.get('lat')
        lon_str = request.args.get('lon')
        workspace = request.args.get('workspace')
        layer_name = request.args.get('layer')
        
        # 验证必要参数
        if not all([lat_str, lon_str, workspace, layer_name]):
            missing_params = []
            if not lat_str:
                missing_params.append('lat')
            if not lon_str:
                missing_params.append('lon')
            if not workspace:
                missing_params.append('workspace')
            if not layer_name:
                missing_params.append('layer')
                
            return jsonify({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }), 400
        
        # 转换经纬度为浮点数
        try:
            lat = float(lat_str)
            lon = float(lon_str)
        except (ValueError, TypeError):
            return jsonify({
                'status': 'error',
                'message': '经纬度必须是有效的数值'
            }), 400
            
        # 检查图层是否存在
        layers = query.get_raster_layers(workspace)
        layer_exists = any(layer['name'] == layer_name for layer in layers)
        
        if not layer_exists:
            return jsonify({
                'status': 'error',
                'message': f'在工作区 {workspace} 中未找到图层 {layer_name}'
            }), 404
            
        # 获取像素值
        pixel_data = query.get_pixel_value(lat, lon, workspace, layer_name)
        
        if pixel_data is None:
            return jsonify({
                'status': 'error',
                'message': f'无法获取坐标点 ({lat}, {lon}) 在图层 {workspace}:{layer_name} 的像素值'
            }), 500
            
        # 构建响应
        response_data = {
            'status': 'success',
            'coordinates': {
                'lat': lat,
                'lon': lon
            },
            'layer': f"{workspace}:{layer_name}",
            'pixel_values': pixel_data.get('values', {}),
        }
        
        # 添加可能的消息
        if 'message' in pixel_data:
            response_data['message'] = pixel_data['message']
            
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/api/query_values', methods=['GET'])
def query_values():
    """
    查询坐标点处的所有栅格图层并获取有效像素值
    
    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)
        
    返回:
        包含有效数据的图层及其像素值
    """
    try:
        # 获取查询参数
        lat_str = request.args.get('lat')
        lon_str = request.args.get('lon')
        workspace = request.args.get('workspace')
        
        # 验证必要参数
        if not all([lat_str, lon_str, workspace]):
            missing_params = []
            if not lat_str:
                missing_params.append('lat')
            if not lon_str:
                missing_params.append('lon')
            if not workspace:
                missing_params.append('workspace')
                
            return jsonify({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }), 400
        
        # 转换经纬度为浮点数
        try:
            lat = float(lat_str)
            lon = float(lon_str)
        except (ValueError, TypeError):
            return jsonify({
                'status': 'error',
                'message': '经纬度必须是有效的数值'
            }), 400
        
        # 执行查询
        result_layers = query.query_point_with_values(lat, lon, workspace)
        
        # 检查是否找到有效图层
        if not result_layers:
            return jsonify({
                'status': 'success',
                'message': f'在坐标点 ({lat}, {lon}) 的工作区 {workspace} 中未找到有有效数据的栅格图层',
                'count': 0,
                'layers': []
            })
        
        return jsonify({
            'status': 'success',
            'count': len(result_layers),
            'coordinates': {
                'lat': lat,
                'lon': lon
            },
            'workspace': workspace,
            'layers': result_layers
        })
        
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

def start_server(host='0.0.0.0', port=5000, debug=False):
    """启动API服务器"""
    logger.info(f"启动GeoServer REST API服务，监听 {host}:{port}")
    app.run(host=host, port=port, debug=debug)

if __name__ == '__main__':
    start_server(debug=True) 